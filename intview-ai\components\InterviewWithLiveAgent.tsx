"use client";
import React, { useState, useEffect } from "react";
import { <PERSON>R<PERSON>, ArrowLeft, Play, Pause } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import LiveStreamingAgent from "./LiveStreamingAgent";
import QuestionsList from "./QuestionsList";
import { useInterview } from "@/context/InterviewContext";

interface InterviewWithLiveAgentProps {
  onNext?: () => void;
  onPrevious?: () => void;
  candidateName?: string;
  jobTitle?: string;
}

const InterviewWithLiveAgent: React.FC<InterviewWithLiveAgentProps> = ({
  onNext,
  onPrevious,
  candidateName = "Jonathan",
  jobTitle = "Insurance Agent",
}) => {
  const {
    questions,
    currentQuestion,
    setCurrentQuestion,
    isInterviewStarted,
    setIsInterviewStarted,
    speakQuestion,
    isStreamConnected,
    agent,
  } = useInterview();

  const [isQuestionPlaying, setIsQuestionPlaying] = useState(false);
  const [hasSpokenCurrentQuestion, setHasSpokenCurrentQuestion] = useState(false);

  // Auto-speak question when interview starts and stream is connected
  useEffect(() => {
    if (
      isInterviewStarted &&
      isStreamConnected &&
      agent &&
      !hasSpokenCurrentQuestion &&
      !isQuestionPlaying
    ) {
      handleSpeakQuestion();
    }
  }, [isInterviewStarted, isStreamConnected, agent, currentQuestion, hasSpokenCurrentQuestion]);

  // Reset spoken flag when question changes
  useEffect(() => {
    setHasSpokenCurrentQuestion(false);
  }, [currentQuestion]);

  const handleSpeakQuestion = async () => {
    if (!isStreamConnected || isQuestionPlaying) return;

    try {
      setIsQuestionPlaying(true);
      await speakQuestion(currentQuestion - 1); // Convert to 0-based index
      setHasSpokenCurrentQuestion(true);
    } catch (error) {
      console.error("Failed to speak question:", error);
    } finally {
      setIsQuestionPlaying(false);
    }
  };

  const handleNextQuestion = () => {
    if (currentQuestion < questions.length) {
      setCurrentQuestion(currentQuestion + 1);
    }
  };

  const handlePreviousQuestion = () => {
    if (currentQuestion > 1) {
      setCurrentQuestion(currentQuestion - 1);
    }
  };

  const handleStartInterview = () => {
    setIsInterviewStarted(true);
    setCurrentQuestion(1);
  };

  const handleFinishInterview = () => {
    setIsInterviewStarted(false);
    if (onNext) {
      onNext();
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Live AI Interview
          </h1>
          <p className="text-gray-600">
            {candidateName} - {jobTitle} Position
          </p>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
          {/* Questions Panel */}
          <div className="lg:col-span-1">
            <QuestionsList className="h-[600px]" />
            
            {/* Question Controls */}
            {isInterviewStarted && (
              <div className="mt-4 space-y-3">
                <div className="flex items-center gap-2">
                  <Button
                    onClick={handleSpeakQuestion}
                    disabled={!isStreamConnected || isQuestionPlaying}
                    variant="outline"
                    size="sm"
                    className="flex items-center gap-2"
                  >
                    {isQuestionPlaying ? (
                      <>
                        <Pause className="w-4 h-4" />
                        Speaking...
                      </>
                    ) : (
                      <>
                        <Play className="w-4 h-4" />
                        Speak Question
                      </>
                    )}
                  </Button>
                </div>

                <div className="flex items-center gap-2">
                  <Button
                    onClick={handlePreviousQuestion}
                    disabled={currentQuestion <= 1}
                    variant="outline"
                    size="sm"
                  >
                    <ArrowLeft className="w-4 h-4" />
                  </Button>
                  
                  <span className="text-sm text-gray-600 px-2">
                    {currentQuestion} of {questions.length}
                  </span>
                  
                  <Button
                    onClick={handleNextQuestion}
                    disabled={currentQuestion >= questions.length}
                    variant="outline"
                    size="sm"
                  >
                    <ArrowRight className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            )}
          </div>

          {/* Live Agent Panel */}
          <div className="lg:col-span-2">
            <LiveStreamingAgent
              className="h-[600px]"
              candidateName={candidateName}
              jobTitle={jobTitle}
              showControls={true}
              showChat={true}
              autoConnect={true}
            />
          </div>
        </div>

        {/* Interview Controls */}
        <div className="flex justify-center gap-4">
          {onPrevious && (
            <Button
              onClick={onPrevious}
              variant="outline"
              size="lg"
              className="flex items-center gap-2"
            >
              <ArrowLeft className="w-5 h-5" />
              Back
            </Button>
          )}

          {!isInterviewStarted ? (
            <Button
              onClick={handleStartInterview}
              disabled={!isStreamConnected || !agent}
              size="lg"
              className="flex items-center gap-2 px-8"
            >
              <Play className="w-5 h-5" />
              Start Interview
            </Button>
          ) : (
            <Button
              onClick={handleFinishInterview}
              size="lg"
              className="flex items-center gap-2 px-8"
            >
              Finish Interview
              <ArrowRight className="w-5 h-5" />
            </Button>
          )}
        </div>

        {/* Status Information */}
        <div className="mt-6 text-center">
          <div className="inline-flex items-center gap-4 text-sm text-gray-600">
            <span className={`flex items-center gap-2 ${agent ? 'text-green-600' : 'text-orange-600'}`}>
              <div className={`w-2 h-2 rounded-full ${agent ? 'bg-green-500' : 'bg-orange-500'}`} />
              Agent: {agent ? 'Ready' : 'Creating...'}
            </span>
            <span className={`flex items-center gap-2 ${isStreamConnected ? 'text-green-600' : 'text-orange-600'}`}>
              <div className={`w-2 h-2 rounded-full ${isStreamConnected ? 'bg-green-500' : 'bg-orange-500'}`} />
              Stream: {isStreamConnected ? 'Connected' : 'Connecting...'}
            </span>
            <span className={`flex items-center gap-2 ${isInterviewStarted ? 'text-blue-600' : 'text-gray-600'}`}>
              <div className={`w-2 h-2 rounded-full ${isInterviewStarted ? 'bg-blue-500' : 'bg-gray-400'}`} />
              Interview: {isInterviewStarted ? 'In Progress' : 'Not Started'}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InterviewWithLiveAgent;
