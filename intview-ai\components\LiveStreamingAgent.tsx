"use client";
import React, { useRef, useEffect, useState } from "react";
import { Bot, Loader2, Play, Square, MessageCircle, Send } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { useInterview } from "@/context/InterviewContext";

interface LiveStreamingAgentProps {
  className?: string;
  candidateName?: string;
  jobTitle?: string;
  showControls?: boolean;
  showChat?: boolean;
  autoConnect?: boolean;
}

const LiveStreamingAgent: React.FC<LiveStreamingAgentProps> = ({
  className = "",
  candidateName = "Jonathan",
  jobTitle = "Insurance Agent",
  showControls = true,
  showChat = false,
  autoConnect = true,
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [messageInput, setMessageInput] = useState("");
  const [isVideoPlaying, setIsVideoPlaying] = useState(false);

  const {
    agent,
    isCreatingAgent,
    agentError,
    createAgent,
    isStreamConnected,
    isStreamConnecting,
    streamError,
    videoStream,
    connectStream,
    disconnectStream,
    sendMessage,
    chatMessages,
  } = useInterview();

  const instructions = `You are an AI interview assistant conducting an interview for the ${jobTitle} position with ${candidateName}. Be professional, engaging, and ask relevant questions about their experience and qualifications. Keep your responses concise and conversational.`;
  const agentName = `${jobTitle} Interviewer`;

  // Create agent on mount
  useEffect(() => {
    if (!agent && !isCreatingAgent) {
      createAgent(instructions, agentName);
    }
  }, [agent, isCreatingAgent, createAgent, instructions, agentName]);

  // Auto-connect stream when agent is ready
  useEffect(() => {
    if (autoConnect && agent && !isStreamConnected && !isStreamConnecting && !streamError) {
      connectStream();
    }
  }, [agent, isStreamConnected, isStreamConnecting, streamError, connectStream, autoConnect]);

  // Set video stream when available
  useEffect(() => {
    if (!videoRef.current) return;

    if (videoStream) {
      // Clear any existing source first
      videoRef.current.src = "";
      videoRef.current.srcObject = videoStream;
      videoRef.current.muted = false;
      videoRef.current.loop = false;
      videoRef.current.play().catch(console.error);
      setIsVideoPlaying(true);
    } else {
      // Clear stream object first
      videoRef.current.srcObject = null;
      // Show idle video
      videoRef.current.src = "/images/emma_idle.mp4";
      videoRef.current.loop = true;
      videoRef.current.muted = true;
      videoRef.current.play().catch(console.error);
      setIsVideoPlaying(false);
    }
  }, [videoStream]);

  const handleSendMessage = async () => {
    if (!messageInput.trim() || !isStreamConnected) return;

    try {
      await sendMessage(messageInput);
      setMessageInput("");
    } catch (error) {
      console.error("Failed to send message:", error);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const playIdleVideo = () => {
    if (videoRef.current) {
      videoRef.current.srcObject = null;
      videoRef.current.src = "/images/emma_idle.mp4";
      videoRef.current.loop = true;
      videoRef.current.muted = true;
      videoRef.current.play().catch(console.error);
    }
  };

  return (
    <div className={`relative ${className}`}>
      <div className="w-full h-full bg-gradient-to-br from-blue-50 to-indigo-100 rounded-2xl flex flex-col overflow-hidden">
        {/* Video Container */}
        <div className="flex-1 relative">
          <video
            ref={videoRef}
            className="w-full h-full object-cover rounded-t-2xl"
            autoPlay
            playsInline
            onLoadStart={() => {
              // Add animation class
              if (videoRef.current) {
                videoRef.current.classList.add("animate-pulse");
                setTimeout(() => {
                  videoRef.current?.classList.remove("animate-pulse");
                }, 1000);
              }
            }}
          />

          {/* Status Overlay */}
          <div className="absolute top-4 left-4 right-4">
            <AnimatePresence>
              {isCreatingAgent && (
                <motion.div
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  className="bg-blue-500/90 text-white px-3 py-2 rounded-lg text-sm flex items-center gap-2"
                >
                  <Loader2 className="w-4 h-4 animate-spin" />
                  Creating AI Agent...
                </motion.div>
              )}

              {isStreamConnecting && (
                <motion.div
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  className="bg-orange-500/90 text-white px-3 py-2 rounded-lg text-sm flex items-center gap-2"
                >
                  <Loader2 className="w-4 h-4 animate-spin" />
                  Connecting Stream...
                </motion.div>
              )}

              {isStreamConnected && (
                <motion.div
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  className="bg-green-500/90 text-white px-3 py-2 rounded-lg text-sm flex items-center gap-2"
                >
                  <div className="w-2 h-2 bg-white rounded-full animate-pulse" />
                  Live
                </motion.div>
              )}

              {(agentError || streamError) && (
                <motion.div
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  className="bg-red-500/90 text-white px-3 py-2 rounded-lg text-sm"
                >
                  {agentError || streamError}
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* Fallback when no agent */}
          {!agent && !isCreatingAgent && (
            <div className="absolute inset-0 flex items-center justify-center bg-gray-100/80">
              <div className="text-center">
                <Bot className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">No agent available</p>
              </div>
            </div>
          )}
        </div>

        {/* Agent Info Bar */}
        {agent && (
          <div className="bg-white/90 backdrop-blur-sm p-3 border-t">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-semibold text-gray-800">{agent.preview_name}</h3>
                <p className="text-sm text-gray-600">
                  {isStreamConnected ? "Connected" : "Ready"}
                </p>
              </div>
              
              {showControls && (
                <div className="flex items-center gap-2">
                  {!isStreamConnected ? (
                    <button
                      onClick={connectStream}
                      disabled={isStreamConnecting || !agent}
                      className="flex items-center gap-2 px-3 py-2 bg-green-500 text-white rounded-lg text-sm hover:bg-green-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                    >
                      <Play className="w-4 h-4" />
                      Connect
                    </button>
                  ) : (
                    <button
                      onClick={disconnectStream}
                      className="flex items-center gap-2 px-3 py-2 bg-red-500 text-white rounded-lg text-sm hover:bg-red-600 transition-colors"
                    >
                      <Square className="w-4 h-4" />
                      Disconnect
                    </button>
                  )}
                </div>
              )}
            </div>
          </div>
        )}

        {/* Chat Interface */}
        {showChat && (
          <div className="bg-white border-t max-h-64 flex flex-col">
            {/* Chat Messages */}
            <div className="flex-1 p-3 overflow-y-auto max-h-40">
              <div className="space-y-2">
                {chatMessages.map((message, index) => (
                  <div
                    key={`${message.role}-${message.timestamp.getTime()}-${index}-${message.content.substring(0, 10)}`}
                    className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
                  >
                    <div
                      className={`max-w-xs px-3 py-2 rounded-lg text-sm ${
                        message.role === 'user'
                          ? 'bg-blue-500 text-white'
                          : 'bg-gray-100 text-gray-800'
                      }`}
                    >
                      {message.content}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Message Input */}
            <div className="p-3 border-t bg-gray-50">
              <div className="flex items-center gap-2">
                <input
                  type="text"
                  value={messageInput}
                  onChange={(e) => setMessageInput(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="Type your message..."
                  disabled={!isStreamConnected}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
                />
                <button
                  onClick={handleSendMessage}
                  disabled={!messageInput.trim() || !isStreamConnected}
                  className="p-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  <Send className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default LiveStreamingAgent;
