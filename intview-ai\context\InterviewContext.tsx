"use client";
import React, { createContext, useContext, useState, useCallback, ReactNode, useRef, useEffect } from "react";
import { DIDLiveStreamingService, Agent } from "@/services/didLiveStreamingService";

interface InterviewContextType {
  // D-ID Agent state
  agent: Agent | null;
  isCreatingAgent: boolean;
  agentError: string | null;
  createAgent: (instructions: string, agentName: string) => Promise<void>;

  // Live streaming state
  isStreamConnected: boolean;
  isStreamConnecting: boolean;
  streamError: string | null;
  videoStream: MediaStream | null;
  connectStream: () => Promise<void>;
  disconnectStream: () => Promise<void>;
  sendMessage: (message: string) => Promise<void>;

  // Chat state
  chatMessages: Array<{ role: 'user' | 'agent'; content: string; timestamp: Date }>;

  // Interview state
  currentQuestion: number;
  setCurrentQuestion: (question: number) => void;
  isInterviewStarted: boolean;
  setIsInterviewStarted: (started: boolean) => void;
  speakQuestion: (questionIndex: number) => Promise<void>;

  // Questions data
  questions: string[];
}

const InterviewContext = createContext<InterviewContextType | undefined>(undefined);

interface InterviewProviderProps {
  children: ReactNode;
}

export const InterviewProvider: React.FC<InterviewProviderProps> = ({ children }) => {
  // D-ID Agent state
  const [agent, setAgent] = useState<Agent | null>(null);
  const [isCreatingAgent, setIsCreatingAgent] = useState<boolean>(false);
  const [agentError, setAgentError] = useState<string | null>(null);

  // Live streaming state
  const [isStreamConnected, setIsStreamConnected] = useState<boolean>(false);
  const [isStreamConnecting, setIsStreamConnecting] = useState<boolean>(false);
  const [streamError, setStreamError] = useState<string | null>(null);
  const [videoStream, setVideoStream] = useState<MediaStream | null>(null);

  // Chat state
  const [chatMessages, setChatMessages] = useState<Array<{ role: 'user' | 'agent'; content: string; timestamp: Date }>>([]);

  // Interview state
  const [currentQuestion, setCurrentQuestion] = useState<number>(1);
  const [isInterviewStarted, setIsInterviewStarted] = useState<boolean>(false);

  // Questions data
  const questions = [
    "Tell us about yourself?",
    "What are your strengths?",
    "Why do you want this job?",
    "Where do you see yourself in 5 years?",
  ];

  // Live streaming service reference
  const streamingServiceRef = useRef<DIDLiveStreamingService | null>(null);

  // Initialize streaming service
  useEffect(() => {
    const apiKey = process.env.NEXT_PUBLIC_DID_API_KEY || process.env.DID_API_KEY || "";
    if (apiKey && !streamingServiceRef.current) {
      streamingServiceRef.current = new DIDLiveStreamingService(apiKey);

      // Set up event handlers
      streamingServiceRef.current.onVideoStatusChange = (isPlaying: boolean, stream?: MediaStream) => {
        if (isPlaying && stream) {
          setVideoStream(stream);
        } else {
          setVideoStream(null);
        }
      };

      streamingServiceRef.current.onConnectionStateChange = (state: RTCPeerConnectionState) => {
        setIsStreamConnected(state === 'connected');
        if (state === 'failed' || state === 'closed') {
          setIsStreamConnecting(false);
          setStreamError('Connection failed');
        }
      };

      streamingServiceRef.current.onAgentMessage = (message: string) => {
        setChatMessages(prev => [...prev, {
          role: 'agent',
          content: message,
          timestamp: new Date()
        }]);
      };

      streamingServiceRef.current.onError = (error: string) => {
        setStreamError(error);
        setIsStreamConnecting(false);
      };
    }
  }, []);

  const createAgent = useCallback(async (instructions: string, agentName: string) => {
    // If agent already exists with same instructions, don't recreate
    if (agent && agent.llm.instructions === instructions && agent.preview_name === agentName) {
      return;
    }

    setIsCreatingAgent(true);
    setAgentError(null);

    try {
      if (!streamingServiceRef.current) {
        throw new Error('Streaming service not initialized');
      }

      const agentData = await streamingServiceRef.current.createAgent(instructions, agentName);
      console.log("D-ID Agent Created Successfully:", agentData);
      setAgent(agentData);

      // Create chat session
      await streamingServiceRef.current.createChatSession(agentData.id);
    } catch (err: unknown) {
      console.error("D-ID Agent Creation Error:", err);
      const errorMessage = err instanceof Error ? err.message : "Failed to create agent";
      setAgentError(`Agent Creation Failed: ${errorMessage}`);
    } finally {
      setIsCreatingAgent(false);
    }
  }, [agent]);

  const connectStream = useCallback(async () => {
    if (!streamingServiceRef.current || !streamingServiceRef.current.isAgentReady()) {
      setStreamError('Agent not ready');
      return;
    }

    setIsStreamConnecting(true);
    setStreamError(null);

    try {
      // Create stream session
      const streamSession = await streamingServiceRef.current.createStreamSession();

      // Create peer connection
      const answer = await streamingServiceRef.current.createPeerConnection(
        streamSession.offer,
        streamSession.ice_servers
      );

      // Start stream
      await streamingServiceRef.current.startStream(answer);

      console.log('Stream connected successfully');

      // Send initial greeting
      setTimeout(async () => {
        if (streamingServiceRef.current && streamingServiceRef.current.isAgentReady()) {
          try {
            await streamingServiceRef.current.sendMessage(
              "Hello! I'm your AI interviewer. I'm ready to begin the interview when you are. Please let me know when you'd like to start."
            );
          } catch (error) {
            console.error('Failed to send greeting:', error);
          }
        }
      }, 2000); // Wait 2 seconds after connection
    } catch (error) {
      console.error('Failed to connect stream:', error);
      const errorMessage = error instanceof Error ? error.message : "Failed to connect stream";
      setStreamError(errorMessage);
    } finally {
      setIsStreamConnecting(false);
    }
  }, []);

  const disconnectStream = useCallback(async () => {
    if (streamingServiceRef.current) {
      await streamingServiceRef.current.destroyStream();
      setIsStreamConnected(false);
      setVideoStream(null);
      setStreamError(null);
    }
  }, []);

  const sendMessage = useCallback(async (message: string) => {
    if (!streamingServiceRef.current || !streamingServiceRef.current.isAgentReady()) {
      throw new Error('Agent not ready');
    }

    // Add user message to chat
    setChatMessages(prev => [...prev, {
      role: 'user',
      content: message,
      timestamp: new Date()
    }]);

    await streamingServiceRef.current.sendMessage(message);
  }, []);

  const speakQuestion = useCallback(async (questionIndex: number) => {
    if (questionIndex < 0 || questionIndex >= questions.length) {
      throw new Error('Invalid question index');
    }

    const question = questions[questionIndex];
    await sendMessage(`Please ask this interview question: "${question}"`);
  }, [questions, sendMessage]);

  const value: InterviewContextType = {
    // D-ID Agent state
    agent,
    isCreatingAgent,
    agentError,
    createAgent,

    // Live streaming state
    isStreamConnected,
    isStreamConnecting,
    streamError,
    videoStream,
    connectStream,
    disconnectStream,
    sendMessage,

    // Chat state
    chatMessages,

    // Interview state
    currentQuestion,
    setCurrentQuestion,
    isInterviewStarted,
    setIsInterviewStarted,
    speakQuestion,

    // Questions data
    questions,
  };

  return (
    <InterviewContext.Provider value={value}>
      {children}
    </InterviewContext.Provider>
  );
};

export const useInterview = (): InterviewContextType => {
  const context = useContext(InterviewContext);
  if (context === undefined) {
    throw new Error('useInterview must be used within an InterviewProvider');
  }
  return context;
};
